<template>
  <div class="inquiry-share-container">
    <!-- Header -->
    <div class="header">
      <div class="header-content">
        <div class="logo-section">
          <div class="company-logo-text">ST</div>
          <div class="company-info">
            <h1 class="company-name">SelecTech</h1>
            <p class="company-subtitle">智能采购管理平台</p>
          </div>
        </div>
        <div class="share-title">
          <h2>询价单分享</h2>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 询价单基本信息 -->
      <div class="inquiry-basic-info">
        <div class="section-header" @click="toggleBasicInfo">
          <font-awesome-icon icon="file-invoice" class="section-icon" />
          <h4>基本信息</h4>
          <font-awesome-icon
            :icon="basicInfoCollapsed ? 'chevron-down' : 'chevron-up'"
            class="collapse-icon"
          />
        </div>

        <div class="info-content" v-show="!basicInfoCollapsed">
          <!-- 企业信息特殊区域 -->
          <div class="company-info-item">
            <div class="company-logo">
              <div class="logo-placeholder">LOGO</div>
            </div>
            <div class="company-details">
              <div class="company-name">{{ inquiryData.companyName }}</div>
              <div class="inquiry-number">询价单号：{{ inquiryData.number }}</div>
            </div>
          </div>

          <!-- 其他信息网格 -->
          <div class="info-grid">
            <div class="info-item">
              <label>询价时间</label>
              <span class="value">{{ formatDate(inquiryData.createdTime) }}</span>
            </div>
            <div class="info-item">
              <label>截止时间</label>
              <span class="value">{{ formatDate(inquiryData.deadline) }}</span>
            </div>
            <div class="info-item">
              <label>询价人</label>
              <span class="value">{{ inquiryData.inquirer }}</span>
            </div>
            <div class="info-item">
              <label>联系电话</label>
              <span class="value">{{ inquiryData.phone }}</span>
            </div>
            <div class="info-item">
              <label>物料型号数</label>
              <span class="value">{{ inquiryData.materialTypeCount }} 种</span>
            </div>
            <div class="info-item">
              <label>已采纳总价</label>
              <span class="value highlight">¥{{ inquiryData.adoptedTotalAmount.toLocaleString() }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 物料明细 -->
      <div class="material-details">
        <div class="section-header">
          <font-awesome-icon icon="boxes" class="section-icon" />
          <h4>物料明细</h4>
          <span class="material-count">共 {{ materials.length }} 项物料</span>
        </div>

        <div class="materials-list">
          <div
            v-for="material in materials"
            :key="material.id"
            class="material-item"
          >
            <div class="material-header">
              <div class="material-info">
                <h4 class="material-name">{{ material.name }}</h4>
                <div class="material-meta">
                  <span class="material-model">型号：{{ material.model }}</span>
                  <span class="material-brand">品牌：{{ material.brand }}</span>
                  <span class="material-category">分类：{{ material.category }}</span>
                </div>
              </div>
              <div class="material-status">
                <span
                  class="status-badge"
                  :style="{ backgroundColor: getStatusColor(material.status) }"
                >
                  {{ getStatusText(material.status) }}
                </span>
              </div>
            </div>

            <div class="material-details-content">
              <div class="detail-row">
                <div class="detail-item">
                  <font-awesome-icon icon="cube" />
                  <span>数量：{{ material.quantity }} 件</span>
                </div>
                <div class="detail-item">
                  <font-awesome-icon icon="calendar" />
                  <span>期望交期：{{ formatDate(material.expectedDate, 'YYYY-MM-DD') }}</span>
                </div>
                <div class="detail-item" :class="{ 'alternative': material.acceptAlternative }">
                  <font-awesome-icon :icon="material.acceptAlternative ? 'check-circle' : 'times-circle'" />
                  <span>{{ material.acceptAlternative ? '接受平替' : '不接受平替' }}</span>
                </div>
              </div>

              <div class="quote-progress">
                <div class="progress-info">
                  <font-awesome-icon icon="chart-bar" />
                  <span>报价进度：</span>
                  <span class="progress-text">
                    供应商数：{{ material.supplierCount }}，
                    已报价：{{ material.quotedCount }}，
                    已拒绝：{{ material.rejectedCount }}
                  </span>
                </div>
              </div>

              <div v-if="material.remark" class="material-remark">
                <font-awesome-icon icon="comment" />
                <span>备注：{{ material.remark }}</span>
              </div>

              <!-- 报价信息 -->
              <div class="quotes-section">
                <div class="quotes-header">
                  <h5>供应商报价</h5>
                  <button
                    v-if="!isLoggedIn"
                    @click="showLoginPrompt"
                    class="login-btn"
                  >
                    <font-awesome-icon icon="lock" />
                    登录查看报价
                  </button>
                </div>

                <div v-if="!isLoggedIn" class="login-required">
                  <font-awesome-icon icon="lock" />
                  <span>请登录查看报价详情</span>
                </div>

                <!-- 登录后显示报价列表 -->
                <div v-else class="quotes-list">
                  <div v-if="material.quotes.length === 0" class="no-quotes">
                    <font-awesome-icon icon="clock" />
                    <span>暂无报价，等待供应商报价中...</span>
                  </div>

                  <div v-else>
                    <div
                      v-for="quote in material.quotes"
                      :key="quote.id"
                      class="quote-item"
                      :class="{
                        'adopted': quote.status === 'adopted',
                        'recommended': quote.isRecommended
                      }"
                    >
                      <!-- 报价头部 -->
                      <div class="quote-header">
                        <div class="supplier-info">
                          <div class="supplier-name">
                            {{ quote.supplierName }}
                            <span v-if="quote.isRecommended" class="recommended-badge">
                              <font-awesome-icon icon="star" />
                              推荐
                            </span>
                            <span v-if="quote.status === 'adopted'" class="adopted-badge">
                              <font-awesome-icon icon="check-circle" />
                              已采纳
                            </span>
                          </div>
                          <div class="supplier-rating">
                            <font-awesome-icon icon="star" />
                            {{ quote.supplierRating }}
                          </div>
                        </div>
                        <div class="quote-time">
                          {{ formatDate(quote.quotedAt, 'MM-DD HH:mm') }}
                        </div>
                      </div>

                      <!-- 报价详情 -->
                      <div class="quote-details">
                        <div class="price-info">
                          <div class="price-item">
                            <label>单价</label>
                            <span class="price">¥{{ quote.unitPrice.toLocaleString() }}</span>
                          </div>
                          <div class="price-item">
                            <label>总价</label>
                            <span class="total-price">¥{{ quote.totalPrice.toLocaleString() }}</span>
                          </div>
                        </div>

                        <div class="delivery-info">
                          <div class="delivery-item">
                            <font-awesome-icon icon="truck" />
                            <span>{{ quote.deliveryDays }}天内发货</span>
                          </div>
                          <div class="delivery-item">
                            <font-awesome-icon icon="calendar" />
                            <span>预计{{ formatDate(quote.deliveryDate, 'MM-DD') }}到货</span>
                          </div>
                          <div class="delivery-item">
                            <font-awesome-icon icon="shield-alt" />
                            <span>质保{{ quote.warranty }}</span>
                          </div>
                        </div>

                        <div v-if="quote.remark" class="quote-remark">
                          <font-awesome-icon icon="comment" />
                          <span>{{ quote.remark }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录提示弹窗 -->
    <nut-popup v-model:visible="loginPromptVisible" position="center" round>
      <div class="login-prompt">
        <div class="prompt-header">
          <font-awesome-icon icon="lock" />
          <h3>需要登录</h3>
        </div>
        <div class="prompt-content">
          <p>查看详细报价信息需要登录账户</p>
        </div>
        <div class="prompt-actions">
          <button class="prompt-btn cancel" @click="loginPromptVisible = false">
            取消
          </button>
          <button class="prompt-btn login" @click="goToLogin">
            去登录
          </button>
        </div>
      </div>
    </nut-popup>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'

const route = useRoute()
const router = useRouter()
const { isLoggedIn } = useAuth()

// 响应式数据
const loginPromptVisible = ref(false)
const basicInfoCollapsed = ref(false)

// 询价单数据
const inquiryData = ref({
  id: 1,
  number: 'RFQ202401001',
  companyName: '上海电气自动化研究所',
  createdTime: new Date('2024-01-15T10:30:00'),
  deadline: new Date('2024-01-22T18:00:00'),
  inquirer: '张工程师',
  phone: '138****8888',
  materialTypeCount: 3,
  adoptedTotalAmount: 8600
})

// 物料数据
const materials = ref([
  {
    id: 1,
    name: '西门子PLC控制器',
    model: '6ES7 214-1BD23-0XB0',
    brand: '西门子',
    category: '控制系统',
    quantity: 2,
    expectedDate: new Date('2024-04-15'),
    acceptAlternative: false,
    status: 'inquiring',
    supplierCount: 5,
    quotedCount: 3,
    rejectedCount: 1,
    remark: '需要原装正品，有CE认证',
    quotes: [
      {
        id: 1,
        supplierName: '上海电气设备有限公司',
        supplierRating: 4.8,
        unitPrice: 2850.00,
        totalPrice: 5700.00,
        deliveryDays: 7,
        deliveryDate: new Date('2024-04-08'),
        warranty: '24个月',
        status: 'quoted',
        quotedAt: new Date('2024-01-16T09:30:00'),
        remark: '原装正品，提供CE认证，包邮',
        isRecommended: true
      },
      {
        id: 2,
        supplierName: '北京自动化科技公司',
        supplierRating: 4.5,
        unitPrice: 2920.00,
        totalPrice: 5840.00,
        deliveryDays: 10,
        deliveryDate: new Date('2024-04-11'),
        warranty: '18个月',
        status: 'quoted',
        quotedAt: new Date('2024-01-16T14:20:00'),
        remark: '全新原装，支持技术指导',
        isRecommended: false
      },
      {
        id: 3,
        supplierName: '深圳工控设备商行',
        supplierRating: 4.2,
        unitPrice: 2780.00,
        totalPrice: 5560.00,
        deliveryDays: 14,
        deliveryDate: new Date('2024-04-15'),
        warranty: '12个月',
        status: 'quoted',
        quotedAt: new Date('2024-01-17T11:45:00'),
        remark: '价格优势，质量保证',
        isRecommended: false
      }
    ]
  },
  {
    id: 2,
    name: '施耐德接触器',
    model: 'LC1D09M7',
    brand: '施耐德',
    category: '电气元件',
    quantity: 5,
    expectedDate: new Date('2024-04-20'),
    acceptAlternative: true,
    status: 'adopted',
    supplierCount: 4,
    quotedCount: 4,
    rejectedCount: 0,
    remark: '可接受正泰等品牌平替',
    quotes: [
      {
        id: 4,
        supplierName: '施耐德电气授权代理',
        supplierRating: 4.9,
        unitPrice: 125.00,
        totalPrice: 625.00,
        deliveryDays: 3,
        deliveryDate: new Date('2024-04-05'),
        warranty: '36个月',
        status: 'adopted',
        quotedAt: new Date('2024-01-16T08:15:00'),
        adoptedAt: new Date('2024-01-17T16:30:00'),
        remark: '官方授权，原装正品，快速发货',
        isRecommended: true
      },
      {
        id: 5,
        supplierName: '正泰电器专营店',
        supplierRating: 4.3,
        unitPrice: 98.00,
        totalPrice: 490.00,
        deliveryDays: 5,
        deliveryDate: new Date('2024-04-07'),
        warranty: '24个月',
        status: 'quoted',
        quotedAt: new Date('2024-01-16T10:45:00'),
        remark: '平替产品，性价比高，质量可靠',
        isRecommended: false
      },
      {
        id: 6,
        supplierName: '德力西电气直销',
        supplierRating: 4.1,
        unitPrice: 105.00,
        totalPrice: 525.00,
        deliveryDays: 7,
        deliveryDate: new Date('2024-04-09'),
        warranty: '24个月',
        status: 'quoted',
        quotedAt: new Date('2024-01-16T15:20:00'),
        remark: '厂家直销，品质保证',
        isRecommended: false
      },
      {
        id: 7,
        supplierName: '常熟开关制造',
        supplierRating: 4.0,
        unitPrice: 92.00,
        totalPrice: 460.00,
        deliveryDays: 10,
        deliveryDate: new Date('2024-04-12'),
        warranty: '18个月',
        status: 'quoted',
        quotedAt: new Date('2024-01-17T09:10:00'),
        remark: '国产品牌，价格实惠',
        isRecommended: false
      }
    ]
  },
  {
    id: 3,
    name: 'ABB变频器',
    model: 'ACS550-01-03A3-4',
    brand: 'ABB',
    category: '传动设备',
    quantity: 1,
    expectedDate: new Date('2024-04-25'),
    acceptAlternative: false,
    status: 'inquiring',
    supplierCount: 3,
    quotedCount: 2,
    rejectedCount: 1,
    remark: '需要配套技术支持',
    quotes: [
      {
        id: 8,
        supplierName: 'ABB电机设备专营',
        supplierRating: 4.7,
        unitPrice: 8500.00,
        totalPrice: 8500.00,
        deliveryDays: 15,
        deliveryDate: new Date('2024-04-18'),
        warranty: '24个月',
        status: 'quoted',
        quotedAt: new Date('2024-01-16T13:25:00'),
        remark: '原装进口，提供技术支持和培训',
        isRecommended: true
      },
      {
        id: 9,
        supplierName: '汇川技术代理商',
        supplierRating: 4.4,
        unitPrice: 7200.00,
        totalPrice: 7200.00,
        deliveryDays: 12,
        deliveryDate: new Date('2024-04-15'),
        warranty: '18个月',
        status: 'quoted',
        quotedAt: new Date('2024-01-17T10:15:00'),
        remark: '国产替代方案，性能稳定',
        isRecommended: false
      }
    ]
  }
])

// 方法
const formatDate = (date, format = 'YYYY-MM-DD HH:mm') => {
  if (!date) return ''
  const d = new Date(date)

  if (format === 'YYYY-MM-DD') {
    return d.getFullYear() + '-' +
           String(d.getMonth() + 1).padStart(2, '0') + '-' +
           String(d.getDate()).padStart(2, '0')
  }

  return d.getFullYear() + '-' +
         String(d.getMonth() + 1).padStart(2, '0') + '-' +
         String(d.getDate()).padStart(2, '0') + ' ' +
         String(d.getHours()).padStart(2, '0') + ':' +
         String(d.getMinutes()).padStart(2, '0')
}

const getStatusColor = (status) => {
  const statusMap = {
    'inquiring': '#1890ff',
    'adopted': '#52c41a',
    'cancelled': '#d9d9d9',
    'expired': '#ff4d4f'
  }
  return statusMap[status] || '#d9d9d9'
}

const getStatusText = (status) => {
  const statusMap = {
    'inquiring': '询价中',
    'adopted': '已采纳',
    'cancelled': '已取消',
    'expired': '已截止'
  }
  return statusMap[status] || '未知'
}

const showLoginPrompt = () => {
  loginPromptVisible.value = true
}

const goToLogin = () => {
  loginPromptVisible.value = false
  router.push('/login')
}

const toggleBasicInfo = () => {
  basicInfoCollapsed.value = !basicInfoCollapsed.value
}

onMounted(() => {
  const inquiryNumber = route.params.inquiryNumber
  console.log('加载询价单数据:', inquiryNumber)
})
</script>

<style scoped>
.inquiry-share-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header样式 */
.header {
  background: linear-gradient(135deg, #f94c30, #e0431b);
  color: white;
  padding: 20px 16px;
  box-shadow: 0 4px 12px rgba(249, 76, 48, 0.3);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.company-logo-text {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: white;
  color: #f94c30;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 700;
}

.company-info {
  display: flex;
  flex-direction: column;
}

.company-name {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
}

.company-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
  line-height: 1.2;
}

.share-title h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

/* 主要内容 */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px 16px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 通用section样式 */
.inquiry-basic-info,
.material-details {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.section-icon {   
  font-size: 20px;
  color: #f94c30;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
  flex: 1;
}

.collapse-icon {
  font-size: 16px;
  color: #666;
  transition: transform 0.2s ease;
}

.material-count {
  font-size: 14px;
  color: #666;
  background: #f8f9fa;
  padding: 4px 12px;
  border-radius: 16px;
}

/* 询价单基本信息 */
.info-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.company-info-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.company-logo {
  flex-shrink: 0;
}

.logo-placeholder {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #f94c30, #e0431b);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
}

.company-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.company-name {
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
  line-height: 1.3;
}

.inquiry-number {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #f94c30;
}

.info-item label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item .value {
  font-size: 13px;
  color: #2a2a35;
  font-weight: 500;
}

.info-item .value.highlight {
  color: #f94c30;
  font-size: 16px;
  font-weight: 600;
}

/* 物料列表 */
.materials-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.material-item {
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 20px;
  background: #fafafa;
  transition: all 0.2s ease;
}

.material-item:hover {
  border-color: #f94c30;
  box-shadow: 0 4px 12px rgba(249, 76, 48, 0.1);
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.material-info {
  flex: 1;
}

.material-name {
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.material-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  font-size: 13px;
  color: #666;
}

.material-model {
  color: #2a2a35;
  font-weight: 500;
}

.material-brand {
  color: #f94c30;
  font-weight: 500;
}

.material-category {
  color: #27ae60;
  font-weight: 500;
}

.material-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

/* 物料详情 */
.material-details-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: white;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
  border: 1px solid #f0f0f0;
}

.detail-item.alternative {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
  border-color: rgba(39, 174, 96, 0.2);
}

.detail-item svg {
  width: 12px;
  height: 12px;
}

.quote-progress {
  background: white;
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #666;
}

.progress-text {
  color: #2a2a35;
  font-weight: 500;
}

.material-remark {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  background: rgba(249, 76, 48, 0.05);
  border-radius: 8px;
  border-left: 4px solid #f94c30;
  font-size: 13px;
  color: #666;
}

.material-remark svg {
  width: 14px;
  height: 14px;
  color: #f94c30;
  margin-top: 2px;
}

/* 报价区域 */
.quotes-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #f0f0f0;
}

.quotes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f5f5f5;
}

.quotes-header h5 {
  font-size: 14px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
}

.login-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #f94c30, #e0431b);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(249, 76, 48, 0.3);
}

.login-required {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.login-required svg {
  width: 16px;
  height: 16px;
}

/* 登录提示弹窗 */
.login-prompt {
  padding: 24px;
  text-align: center;
  max-width: 320px;
}

.prompt-header {
  margin-bottom: 16px;
}

.prompt-header svg {
  font-size: 32px;
  color: #f94c30;
  margin-bottom: 8px;
}

.prompt-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
}

.prompt-content {
  margin-bottom: 20px;
}

.prompt-content p {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin: 0;
}

.prompt-actions {
  display: flex;
  gap: 12px;
}

.prompt-btn {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.prompt-btn.cancel {
  border-color: #d9d9d9;
  color: #999;
}

.prompt-btn.cancel:hover {
  background: #f5f5f5;
}

.prompt-btn.login {
  background: linear-gradient(135deg, #f94c30, #e0431b);
  border-color: #f94c30;
  color: white;
}

.prompt-btn.login:hover {
  box-shadow: 0 2px 8px rgba(249, 76, 48, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }

  /* .info-grid {
    grid-template-columns: 1fr;
  }

  .company-info-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .company-details {
    align-items: center;
  } */

  .material-header {
    flex-direction: column;
    gap: 12px;
  }

  .material-meta {
    flex-direction: column;
    gap: 8px;
  }

  .detail-row {
    flex-direction: column;
    gap: 8px;
  }

  .prompt-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 16px 12px;
  }

  .inquiry-basic-info,
  .material-details {
    padding: 16px;
  }

  .material-item {
    padding: 16px;
  }

  .quotes-section {
    padding: 12px;
  }
}

/* 报价列表样式 */
.quotes-list {
  margin-top: 8px;
}

.no-quotes {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.no-quotes svg {
  width: 16px;
  height: 16px;
}

.quote-item {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.quote-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.quote-item.recommended {
  border-color: #ffd700;
  background: linear-gradient(135deg, #fffbf0, #fefcf3);
}

.quote-item.adopted {
  border-color: #52c41a;
  background: linear-gradient(135deg, #f6ffed, #f0f9e8);
}

.quote-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.supplier-info {
  flex: 1;
}

.supplier-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #2a2a35;
  margin-bottom: 4px;
}

.recommended-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: #ffd700;
  color: #d48806;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

.adopted-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: #52c41a;
  color: white;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

.supplier-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #ffa940;
}

.quote-time {
  font-size: 12px;
  color: #999;
}

.quote-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.price-info {
  display: flex;
  gap: 24px;
}

.price-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.price-item label {
  font-size: 12px;
  color: #666;
}

.price {
  font-size: 16px;
  font-weight: 600;
  color: #f94c30;
}

.total-price {
  font-size: 18px;
  font-weight: 700;
  color: #f94c30;
}

.delivery-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.delivery-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.delivery-item svg {
  width: 12px;
  height: 12px;
  color: #999;
}

.quote-remark {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(249, 76, 48, 0.05);
  border-radius: 6px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.quote-remark svg {
  width: 12px;
  height: 12px;
  color: #f94c30;
  margin-top: 2px;
  flex-shrink: 0;
}
</style>